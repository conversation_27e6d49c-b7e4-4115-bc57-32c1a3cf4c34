# 智能打包文件命名示例

## 实际使用场景示例

### 场景1：单个项目文件夹上传

**上传内容**：
```
MyReactProject/
├── src/
│   ├── components/
│   └── pages/
├── public/
└── package.json
```

**生成的压缩包名**：`MyReactProject.7z`

**说明**：单文件夹上传场景，直接使用文件夹名称。

---

### 场景2：多个文档文件上传

**上传内容**：
- `项目需求文档.pdf`
- `设计稿.psd`
- `用户手册.docx`

**生成的压缩包名**：`项目需求文档.pdf,设计稿.psd,用户手册.docx.7z`

**说明**：少于5个文件，全部列出文件名。

---

### 场景3：大量图片文件上传

**上传内容**：
- `photo1.jpg`
- `photo2.jpg`
- `photo3.jpg`
- `photo4.jpg`
- `photo5.jpg`
- `photo6.jpg`
- `photo7.jpg`
- `photo8.jpg`

**生成的压缩包名**：`photo1.jpg,photo2.jpg,photo3.jpg,photo4.jpg,photo5.jpg等文件.7z`

**说明**：超过5个文件，只显示前5个并添加"等文件"后缀。

---

### 场景4：混合内容上传

**上传内容**：
- `README.md`（单独文件）
- `src/`（文件夹）
- `docs/`（文件夹）
- `tests/`（文件夹）
- `config.json`（单独文件）
- `assets/`（文件夹）

**生成的压缩包名**：`README.md,src,docs,tests,config.json等文件.7z`

**说明**：混合上传包含文件和文件夹，按顺序列出前5个。

---

### 场景5：特殊字符处理

**上传内容**：
- `文件 with spaces.txt`
- `file<>:with|special*chars.pdf`
- `normal_file.docx`

**生成的压缩包名**：`文件_with_spaces.txt,file_with_special_chars.pdf,normal_file.docx.7z`

**说明**：特殊字符被清理为下划线，确保文件系统兼容性。

---

### 场景6：超长文件名截断

**上传内容**：
- `VeryLongFileNameThatExceedsTheNormalLengthLimit1.txt`
- `VeryLongFileNameThatExceedsTheNormalLengthLimit2.txt`
- `VeryLongFileNameThatExceedsTheNormalLengthLimit3.txt`
- `VeryLongFileNameThatExceedsTheNormalLengthLimit4.txt`
- `VeryLongFileNameThatExceedsTheNormalLengthLimit5.txt`
- `VeryLongFileNameThatExceedsTheNormalLengthLimit6.txt`

**生成的压缩包名**：`VeryLongFileNameThatExceedsTheNormalLengthLimit1.txt,VeryLongFileNameThatExceedsTheNormalLengthLimit2.txt等文件.7z`

**说明**：当组合名称超过100字符时，在逗号处截断但保持"等文件"后缀。

---

### 场景7：单个超长文件夹名

**上传内容**：
```
VeryLongProjectNameThatExceedsTheMaximumLengthLimitAndShouldBeTruncatedProperly/
├── file1.txt
└── file2.txt
```

**生成的压缩包名**：`VeryLongProjectNameThatExceedsTheMaximumLengthLimitAndShouldBeTruncatedProperl....7z`

**说明**：单文件夹名超过100字符时，截断并添加"..."。

---

### 场景8：拖拽上传文件夹

**拖拽内容**：整个项目文件夹
```
WebApp/
├── frontend/
├── backend/
└── database/
```

**生成的压缩包名**：`WebApp.7z`

**说明**：拖拽单个文件夹时，使用根文件夹名称。

---

### 场景9：文件夹选择优先级

**选择的文件夹路径**：`/Users/<USER>/Projects/MyApp`

**包含的文件**：多个子文件和文件夹

**生成的压缩包名**：`MyApp.7z`

**说明**：当提供文件夹路径参数时，优先使用路径中的文件夹名称。

---

### 场景10：回退命名

**上传内容**：无法识别的文件结构或空文件列表

**生成的压缩包名**：`batch_upload_1703123456789.7z`

**说明**：当无法生成合适名称时，使用时间戳作为回退方案。

---

## 命名逻辑优势

1. **直观性**：用户可以从压缩包名称快速了解内容
2. **唯一性**：不同的上传内容生成不同的名称
3. **安全性**：清理特殊字符，确保文件系统兼容
4. **长度控制**：避免文件名过长导致的问题
5. **智能截断**：保持重要信息的同时控制长度

## 技术实现亮点

- **场景识别**：自动识别单文件夹、多文件、混合上传等场景
- **优先级处理**：folderPath > 单文件夹分析 > 多文件组合 > 回退方案
- **字符清理**：全面的特殊字符处理和文件系统兼容性
- **智能截断**：在合适位置截断，保持可读性
- **日志记录**：完整的命名过程日志，便于调试和监控
