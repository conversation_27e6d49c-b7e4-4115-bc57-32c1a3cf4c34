# 智能打包文件命名逻辑优化

## 概述

本次优化针对智能打包功能生成的7z压缩文件命名逻辑进行了全面改进，实现了更智能、更用户友好的文件命名规则。

## 优化目标

1. **智能命名规则**：根据不同上传场景生成合适的文件名
2. **长度控制**：确保文件名不超过100字符限制
3. **特殊字符处理**：清理不安全的文件系统字符
4. **用户友好**：生成易于理解和识别的文件名

## 命名规则详解

### 1. 单文件夹上传场景

**适用条件**：上传内容为单个文件夹时

**命名规则**：使用该文件夹的名称作为压缩包名

**示例**：
- 上传文件夹 `MyProject` → 压缩包名：`MyProject.7z`
- 上传文件夹 `Documents` → 压缩包名：`Documents.7z`

### 2. 多文件/多文件夹场景

**适用条件**：上传内容包含多个文件或文件夹时

**命名规则**：`${文件1,文件2,文件3,文件4,文件5}等文件`

**详细说明**：
- 只提取前5个文件/文件夹的名称进行拼接
- 使用逗号分隔文件名
- 如果超过5个文件，添加"等文件"后缀
- 如果只有1个文件，直接使用文件名（不加"等文件"）

**示例**：
- 3个文件：`doc1.pdf,image1.jpg,video1.mp4.7z`
- 7个文件：`file1.txt,file2.txt,file3.txt,file4.txt,file5.txt等文件.7z`
- 混合上传：`standalone.pdf,Project1,Project2等文件.7z`

## 长度限制处理

### 基本限制
- 最终文件名（不包含.7z扩展名）最大长度：**100个字符**
- 超过限制时进行智能截断

### 截断策略

1. **保持"等文件"后缀**：如果文件名以"等文件"结尾，截断时确保保持这个后缀
2. **避免中间截断**：在逗号位置截断，避免在文件名中间截断
3. **单文件夹名截断**：对于单文件夹场景，使用"..."表示截断

**截断示例**：
```
原始名称：VeryLongFileName0,VeryLongFileName1,VeryLongFileName2,VeryLongFileName3,VeryLongFileName4等文件
截断后：VeryLongFileName0,VeryLongFileName1等文件
```

## 特殊字符处理

### 清理规则
- 替换文件系统不允许的字符：`< > : " / \ | ? *` → `_`
- 将空格替换为下划线：` ` → `_`
- 合并多个连续下划线：`__` → `_`
- 移除开头和结尾的下划线

### 处理示例
```
原始：file with spaces.txt → 清理后：file_with_spaces.txt
原始：file<>:with|special*chars.txt → 清理后：file_with_special_chars.txt
```

## 实现细节

### 核心函数

#### `generateSmartPackArchiveName(files, analysis?, folderPath?)`
主要的命名生成函数，根据不同场景生成合适的文件名。

#### `sanitizeFileName(fileName)`
清理文件名中的特殊字符，确保符合文件系统命名规范。

#### `extractFileOrFolderName(filePath)`
从文件路径中提取文件或文件夹名称。

### 优先级逻辑

1. **folderPath参数**：如果提供了文件夹路径参数，优先使用
2. **单文件夹分析**：如果分析结果显示为单文件夹上传，使用文件夹名
3. **多文件组合**：根据文件和文件夹名称生成组合名称
4. **回退方案**：如果无法生成合适名称，使用时间戳回退

## 使用场景

### 1. 文件夹选择上传
```typescript
const archiveName = generateSmartPackArchiveName(files, analysis, folderPath);
// folderPath: "/Users/<USER>/MyProject"
// 结果: "MyProject"
```

### 2. 拖拽多文件上传
```typescript
const archiveName = generateSmartPackArchiveName(files, analysis);
// files: [file1.txt, file2.txt, file3.txt, ...]
// 结果: "file1.txt,file2.txt,file3.txt,file4.txt,file5.txt等文件"
```

### 3. 混合内容上传
```typescript
const archiveName = generateSmartPackArchiveName(files, analysis);
// 包含单独文件和文件夹
// 结果: "document.pdf,Project1,Project2等文件"
```

## 日志记录

优化后的命名逻辑包含详细的日志记录：

```typescript
tusLogger.analysis(`📦 生成压缩包名称: ${archiveName}`);
```

这有助于调试和监控命名逻辑的执行情况。

## 兼容性

- 保持与现有智能打包功能的完全兼容
- 不影响现有的上传流程和错误处理
- 向后兼容原有的命名逻辑作为回退方案

## 测试验证

创建了完整的测试用例覆盖各种场景：
- 单文件夹上传
- 多文件上传（少于和超过5个文件）
- 混合上传（文件+文件夹）
- 特殊字符处理
- 长度限制截断
- 优先级逻辑

测试文件位置：`src/components/Upload/composables/__tests__/smartPackNaming.test.ts`

## 总结

通过这次优化，智能打包功能的文件命名变得更加智能和用户友好，能够根据不同的上传场景生成合适的文件名，同时确保文件名的安全性和可读性。
