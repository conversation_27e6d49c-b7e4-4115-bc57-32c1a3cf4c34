import { onMounted, onUnmounted } from "vue";
import { useGlobalProgress } from "./useGlobalProgress";

/**
 * 压缩进度监听 Composable
 * 监听主进程的压缩任务事件，并同步到全局进度管理系统
 */
export function useArchiveProgress() {
  // 🔧 修复：使用单一的useGlobalProgress实例，避免多实例问题
  const globalProgress = useGlobalProgress();
  const { addArchiveTask, updateArchiveTaskProgress, completeArchiveTask, errorArchiveTask, activeTasks } = globalProgress;

  let isListenersSetup = false;

  /**
   * 获取 Electron API
   */
  const getElectronAPI = () => (window as any).electronAPI;

  /**
   * 设置压缩事件监听器
   */
  const setupArchiveEventListeners = () => {
    if (isListenersSetup) {
      return;
    }

    try {
      const api = getElectronAPI();

      if (!api || !api.archive) {
        console.warn("Electron Archive API 不可用");
        return;
      }

      // 监听压缩任务创建事件
      api.archive.onTaskCreated((taskId: string, task: any) => {
        // 创建前端进度任务
        const fileName = task.name ? `${task.name}.7z` : `archive_${Date.now()}.7z`;
        const totalFiles = task.totalFiles || 0;

        addArchiveTask(taskId, fileName, totalFiles);
      });

      // 监听压缩任务进度事件
      api.archive.onTaskProgress((taskId: string, progress: number, currentFile?: string) => {
        // 检查任务是否存在，如果不存在则创建
        const existingTask = activeTasks.value.find((t) => t.archiveTaskId === taskId);
        if (!existingTask) {
          // 从任务ID推断文件名
          const fileName = taskId.includes("2G多UE工程文件") ? "2G多UE工程文件.7z" : `archive_${Date.now()}.7z`;
          addArchiveTask(taskId, fileName, 1603); // 使用已知的文件数量
        }

        // 更新前端进度
        updateArchiveTaskProgress(taskId, progress, currentFile);
      });

      // 监听压缩任务完成事件
      api.archive.onTaskCompleted((taskId: string, result: any) => {
        // 完成前端任务
        completeArchiveTask(taskId, result?.archivePath);
      });

      // 监听压缩任务错误事件
      api.archive.onTaskError((taskId: string, error: string) => {
        console.error(`压缩任务出错: ${taskId} - ${error}`);

        // 标记前端任务为错误状态
        errorArchiveTask(taskId, error);
      });

      // 监听压缩任务取消事件
      api.archive.onTaskCancelled((taskId: string) => {
        // 标记前端任务为取消状态
        errorArchiveTask(taskId, "用户取消");
      });

      isListenersSetup = true;
    } catch (error) {
      console.error("设置压缩事件监听器失败:", error);
    }
  };

  /**
   * 清理事件监听器
   */
  const cleanupArchiveEventListeners = () => {
    if (!isListenersSetup) {
      return;
    }

    try {
      const api = getElectronAPI();

      if (api && api.archive && api.archive.removeAllListeners) {
        api.archive.removeAllListeners();
      }

      isListenersSetup = false;
    } catch (error) {
      console.error("清理压缩事件监听器失败:", error);
    }
  };

  // 立即设置监听器（不等待组件挂载）
  setupArchiveEventListeners();

  // 组件挂载时确保监听器已设置
  onMounted(() => {
    if (!isListenersSetup) {
      setupArchiveEventListeners();
    }
  });

  // 组件卸载时清理监听器
  onUnmounted(() => {
    cleanupArchiveEventListeners();
  });

  return {
    setupArchiveEventListeners,
    cleanupArchiveEventListeners,
    isListenersSetup: () => isListenersSetup,
  };
}
