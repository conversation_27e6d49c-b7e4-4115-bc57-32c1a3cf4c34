import { ref, onMounted, onUnmounted, nextTick } from 'vue'

/**
 * 检测文本是否被截断的 composable
 * 用于动态判断是否需要显示 tooltip
 */
export function useTextTruncation() {
  const elementRef = ref<HTMLElement>()
  const isTruncated = ref(false)

  /**
   * 检查元素是否被截断
   */
  const checkTruncation = async () => {
    await nextTick()
    
    if (!elementRef.value) {
      isTruncated.value = false
      return
    }

    const element = elementRef.value
    
    // 检查水平截断（text-overflow: ellipsis）
    const isHorizontallyTruncated = element.scrollWidth > element.clientWidth
    
    // 检查垂直截断（多行文本）
    const isVerticallyTruncated = element.scrollHeight > element.clientHeight
    
    isTruncated.value = isHorizontallyTruncated || isVerticallyTruncated
  }

  /**
   * 防抖的检查函数
   */
  let checkTimeout: NodeJS.Timeout | null = null
  const debouncedCheck = () => {
    if (checkTimeout) {
      clearTimeout(checkTimeout)
    }
    checkTimeout = setTimeout(checkTruncation, 100)
  }

  /**
   * ResizeObserver 用于监听元素大小变化
   */
  let resizeObserver: ResizeObserver | null = null

  onMounted(() => {
    // 初始检查
    checkTruncation()

    // 监听窗口大小变化
    window.addEventListener('resize', debouncedCheck)

    // 使用 ResizeObserver 监听元素大小变化
    if (elementRef.value && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(debouncedCheck)
      resizeObserver.observe(elementRef.value)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', debouncedCheck)
    
    if (checkTimeout) {
      clearTimeout(checkTimeout)
    }

    if (resizeObserver) {
      resizeObserver.disconnect()
    }
  })

  return {
    elementRef,
    isTruncated,
    checkTruncation
  }
}

/**
 * 简化版本的文本截断检测 composable
 * 适用于不需要响应式监听的场景
 */
export function useSimpleTextTruncation() {
  /**
   * 检查指定元素是否被截断
   */
  const checkElementTruncation = (element: HTMLElement): boolean => {
    if (!element) return false
    
    // 检查水平截断
    const isHorizontallyTruncated = element.scrollWidth > element.clientWidth
    
    // 检查垂直截断
    const isVerticallyTruncated = element.scrollHeight > element.clientHeight
    
    return isHorizontallyTruncated || isVerticallyTruncated
  }

  return {
    checkElementTruncation
  }
}
