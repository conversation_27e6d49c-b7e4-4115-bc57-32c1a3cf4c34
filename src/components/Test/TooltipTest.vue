<template>
  <div class="p-4 space-y-4">
    <h2 class="text-lg font-semibold">Tooltip 功能测试</h2>
    
    <!-- 测试历史任务项 -->
    <div class="space-y-2">
      <h3 class="text-md font-medium">历史任务项测试</h3>
      <div class="w-80 border rounded p-2">
        <HistoryTaskItem :task="testHistoryTask" />
      </div>
    </div>

    <!-- 测试批量历史任务项 -->
    <div class="space-y-2">
      <h3 class="text-md font-medium">批量历史任务项测试</h3>
      <div class="w-80 border rounded p-2">
        <BatchHistoryTaskItem 
          :batch-task="testBatchHistoryTask" 
          :expanded="false"
        />
      </div>
    </div>

    <!-- 测试不同长度的文件名 -->
    <div class="space-y-2">
      <h3 class="text-md font-medium">不同长度文件名测试</h3>
      <div class="space-y-2">
        <div class="w-60 border rounded p-2">
          <HistoryTaskItem :task="shortNameTask" />
        </div>
        <div class="w-60 border rounded p-2">
          <HistoryTaskItem :task="longNameTask" />
        </div>
        <div class="w-60 border rounded p-2">
          <HistoryTaskItem :task="veryLongNameTask" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HistoryTaskItem from '@/components/GlobalProgressIndicator/HistoryTaskItem.vue'
import BatchHistoryTaskItem from '@/components/GlobalProgressIndicator/BatchHistoryTaskItem.vue'
import type { HistoryTask, BatchHistoryTask } from '@/composables/useGlobalProgress'

// 测试用的历史任务数据
const testHistoryTask: HistoryTask = {
  id: 'test-1',
  type: 'upload',
  fileName: '这是一个非常长的文件名用来测试tooltip功能是否正常工作.pdf',
  size: '2.5 MB',
  startTime: new Date('2024-01-01T10:00:00'),
  endTime: new Date('2024-01-01T10:02:30'),
  duration: 150000,
  status: 'completed'
}

// 测试用的批量历史任务数据
const testBatchHistoryTask: BatchHistoryTask = {
  id: 'batch-test-1',
  type: 'batch',
  batchType: 'upload',
  batchName: '这是一个非常长的批量任务名称用来测试tooltip功能是否正常工作的文件夹',
  totalFiles: 10,
  completedFiles: 8,
  failedFiles: 2,
  totalSize: 52428800, // 50MB
  status: 'completed',
  startTime: new Date('2024-01-01T10:00:00'),
  endTime: new Date('2024-01-01T10:05:00'),
  duration: 300000,
  subTasks: [
    {
      id: 'sub-1',
      fileName: '短文件名.txt',
      fileSize: 1024,
      status: 'completed'
    },
    {
      id: 'sub-2',
      fileName: '这是一个非常长的子任务文件名用来测试tooltip功能是否正常工作在子任务中.docx',
      fileSize: 2048000,
      status: 'completed'
    },
    {
      id: 'sub-3',
      fileName: '另一个超级长的文件名测试用例包含很多字符来验证截断和tooltip显示效果.xlsx',
      fileSize: 5120000,
      status: 'error'
    }
  ]
}

// 不同长度的文件名测试
const shortNameTask: HistoryTask = {
  id: 'short-1',
  type: 'download',
  fileName: '短文件名.txt',
  size: '1 KB',
  startTime: new Date(),
  endTime: new Date(),
  duration: 1000,
  status: 'completed'
}

const longNameTask: HistoryTask = {
  id: 'long-1',
  type: 'upload',
  fileName: '中等长度的文件名测试用例.pdf',
  size: '500 KB',
  startTime: new Date(),
  endTime: new Date(),
  duration: 5000,
  status: 'completed'
}

const veryLongNameTask: HistoryTask = {
  id: 'very-long-1',
  type: 'download',
  fileName: '这是一个超级超级长的文件名用来测试tooltip功能在各种情况下的表现包含中文英文数字和特殊字符123456789.zip',
  size: '10 MB',
  startTime: new Date(),
  endTime: new Date(),
  duration: 30000,
  status: 'error',
  error: '下载失败：网络连接超时'
}
</script>
