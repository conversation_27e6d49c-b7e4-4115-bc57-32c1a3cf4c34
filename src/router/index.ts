import { createRouter, createWeb<PERSON>ashHist<PERSON> } from "vue-router";
import { createRouteGuard } from "@/composables/useAuthGuard";

const routes = [
  {
    path: "/",
    redirect: "/resources",
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
    meta: {
      requiresAuth: false,
      isPublic: true,
    },
  },
  {
    path: "/resources",
    name: "Resources",
    component: () => import("@/views/Resources.vue"),
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: ":folderType/:pathMatch(.*)*",
        name: "FolderView",
        component: () => import("@/views/Folders/FolderView.vue"),
        props: true,
        meta: {
          requiresAuth: true,
        },
      },
      {
        path: ":folderType",
        name: "FolderRoot",
        component: () => import("@/views/Folders/FolderView.vue"),
        props: true,
        meta: {
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: "/test/tooltip",
    name: "TooltipTest",
    component: () => import("@/components/Test/TooltipTest.vue"),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: "/trash",
    name: "Trash",
    component: () => import("@/views/Trash.vue"),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: "/tags",
    name: "Tags",
    component: () => import("@/views/Tags/index.vue"),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: "/permissions",
    name: "Permissions",
    component: () => import("@/views/Permissions.vue"),
    meta: {
      requiresAuth: true,
    },
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 使用新的路由守卫
router.beforeEach(createRouteGuard());

export default router;
